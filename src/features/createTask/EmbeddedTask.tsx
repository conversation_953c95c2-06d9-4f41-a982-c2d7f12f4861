import { useState, useEffect, useRef } from "react";
import { Minus, Plus, Loader2 } from "lucide-react";
import { AnimatePresence, motion } from "motion/react"
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";

import debounce from "lodash.debounce";
import { useToast } from "@/hooks/use-toast";
import CuteAIIcon from "@/assets/cute-ai.svg";
import * as <PERSON>lt<PERSON> from "@radix-ui/react-tooltip";
import { Info } from "lucide-react";
import { useAuth } from "@/contexts";
import DockerImageSelector from "@/components/DockerImageSelector";
import SkillsSelector from "@/components/SkillsSelector";
import AttachmentCross from "@/assets/attachment-cross.svg";
import AttachIcon from "@/assets/attach.svg";
import { useImageAttachments } from "@/hooks/useImageAttachments";
import TextareaWithAttachmentV2 from "@/components/TextareaWithAttachmentV2";
import AuthModal from "@/components/modals/AuthModal";
import SuggestionChips from "@/components/SuggestionChips";
import { trackTaskCreation, trackUserSource, isExperimentalEnabled, trackExperimentalFeature, isInputDisabled } from "@/services/postHogService";

// Assets
import AdvancedControls from "@/assets/advanced-controls.svg"
import CloseSVG from "@/assets/close.svg"
import { useTabState } from "@/components/TabBar";
import CoinIconSVG from "@/assets/copper-coin.svg"
import { useGitHub } from "@/hooks/useGitHubAPI";
import { useConfig } from "@/hooks/useConfig";
import { RepositorySelectorV2, RepositorySelectorRef } from "@/components/RepositorySelectorV2";
import useScreenSize from "@/hooks/useScreenSize";

interface Skill {
    id: string;
    name: string;
    latest_version: string;
    job_id: string;
    created_at: string;
    updated_at: string;
}

interface TaskFormState {
    taskInput: string;
    selectedImage: string;
    selectedAgent: string;
    selectedModel: string;
    perInstanceCostLimit: string;
    selectedSkills: string[];
    selectedImages: Array<{
        mime_type: string;
        img_base64: string;
    }>;
    loadingImages: boolean;
    githubUrl: string;
    branchName: string;
    projectType: string;
}

interface CreateTaskProps {
    tabId: string;
    onClearTabId: () => void
}

export default function EmbeddedTask({ tabId, onClearTabId }: CreateTaskProps) {
    const { toast } = useToast();
    const { config: globalConfig, loading: configLoading } = useConfig();
    const { getTabState, updateTabState, setTabs, setActiveTab } = useTabState();
    const { session, user } = useAuth();
    const { isConnected: hasUserConnectedGithub } = useGitHub();
    
    // Centralized form state
    const [formState, setFormState] = useState<TaskFormState>({
        taskInput: "",
        selectedImage: "",
        selectedAgent: "",
        selectedModel: "",
        perInstanceCostLimit: "5",
        selectedSkills: [],
        selectedImages: [],
        loadingImages: false,
        githubUrl: "",
        branchName: "",
        projectType: "new_app"
    });

    // UI state
    const [showControls, setShowControls] = useState(false);
    const [showGithubSettings, setShowGithubSettings] = useState(false);
    const [isTransitioning, setIsTransitioning] = useState(false);
    const [showChips, setShowChips] = useState(true);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [experimental, setExperimental] = useState(false);
    const [skills, setSkills] = useState<Skill[]>([]);
    const { isMobile } = useScreenSize();

    // Refs
    const submissionInProgress = useRef(false);
    const repositorySelectorRef = useRef<RepositorySelectorRef>(null);
    const initializedTabs = useRef<Set<string>>(new Set());

    // Centralized state update function
    const updateFormState = (updates: Partial<TaskFormState>) => {
        setFormState(prev => ({ ...prev, ...updates }));
    };

    // Track user source when component mounts
    useEffect(() => {
        trackUserSource({
            component: 'EmbeddedTask',
            userId: user?.id,
            isAuthenticated: !!session
        });
    }, [user?.id, session]);

    // Initialize form state from config and tab state
    useEffect(() => {
        if (!tabId || !globalConfig) return;

        // Only initialize tab state once per tab ID
        if (initializedTabs.current.has(tabId)) return;

        const tabState = getTabState(tabId);
        const formLayout = globalConfig.form_layouts?.new_app?.cloud;
        const defaults = formLayout?.defaults || {};

        // Initialize form state with defaults and tab state
        updateFormState({
            taskInput: tabState.task || defaults.taskDescription || "",
            selectedImage: tabState.selectedImage || defaults.dockerImage || "",
            selectedAgent: tabState.selectedAgent || defaults.agent || "",
            selectedModel: tabState.model_name || defaults.baseModel || "",
            perInstanceCostLimit: tabState.per_instance_cost_limit || defaults.budget?.toString() || "5",
            selectedSkills: tabState.agentic_skills || defaults.skills || [],
            selectedImages: tabState.selectedImages || [],
            githubUrl: tabState.repository || "",
            branchName: tabState.branch || "",
            projectType: tabState.projectType || "new_app"
        });

        // Mark this tab as initialized
        initializedTabs.current.add(tabId);
    }, [globalConfig, getTabState, tabId]);

    // Update tab state when form state changes
    useEffect(() => {
        if (!tabId || initializedTabs.current.size === 0) return;

        const updateData = {
            task: formState.taskInput,
            selectedImage: formState.selectedImage,
            image: formState.selectedImage, // Duplicate for backward compatibility
            selectedAgent: formState.selectedAgent,
            model_name: formState.selectedModel,
            per_instance_cost_limit: formState.perInstanceCostLimit,
            agentic_skills: formState.selectedSkills,
            selectedImages: formState.selectedImages,
            repository: formState.githubUrl,
            branch: formState.branchName,
            projectType: formState.projectType,
            isCloudFlow: true // Always cloud
        };

        updateTabState(tabId, updateData);
    }, [formState, tabId, updateTabState]);

    // Initialize skills from config
    useEffect(() => {
        if (globalConfig?.skills) {
            setSkills(globalConfig.skills);
        }
    }, [globalConfig]);

    // Update selected skills when agent changes
    useEffect(() => {
        if (formState.selectedAgent && globalConfig) {
            const selectedAgentConfig = globalConfig.agent_names?.find(
                (agent: any) => agent.name === formState.selectedAgent
            );

            if (selectedAgentConfig?.sub_agents) {
                const validSubAgents = selectedAgentConfig.sub_agents.filter(
                    (subAgent: any) => skills.some((skill) => skill.name === subAgent)
                );
                updateFormState({ selectedSkills: validSubAgents });
            } else {
                updateFormState({ selectedSkills: [] });
            }
        }
    }, [formState.selectedAgent, skills, globalConfig]);

    // Show chips again when textarea is cleared
    useEffect(() => {
        if (formState.taskInput === "" && !showChips) {
            setShowChips(true);
        }
    }, [formState.taskInput, showChips]);

    // Check experimental features
    useEffect(() => {
        if (!user) return;

        const experimentalEnabled = isExperimentalEnabled();
        setExperimental(experimentalEnabled);

        trackExperimentalFeature(experimentalEnabled, {
            userId: user.id,
            component: 'EmbeddedTask'
        });
    }, [user]);

    // Initialize tab
    useEffect(() => {
        setTabs((prevTabs) => {
            const tabExists = prevTabs.some(tab => tab.id === tabId);
            if (!tabExists) {
                return [
                    ...prevTabs,
                    {
                        id: tabId,
                        title: "Setting up Task",
                        path: "/not-defined",
                        state: {
                            tabId: tabId,
                            sourceTabId: tabId,
                            isCloudFlow: true,
                        },
                        status: false,
                        isCloudFlow: true,
                    },
                ];
            }
            return prevTabs;
        });

        updateTabState(tabId, { isCloudFlow: true });
    }, [tabId]);

    // Image attachments hook
    const imageAttachments = useImageAttachments({
        maxImages: 5,
        maxSizeInMB: 5,
        maxPixelDimensions: 8000,
        maxPixelDimensionsMultiple: 2000,
        onImagesChange: (images) => updateFormState({ selectedImages: images })
    });

    // Set loading state for images
    useEffect(() => {
        updateFormState({ loadingImages: imageAttachments.isProcessing });
    }, [imageAttachments.isProcessing]);

    // Event handlers
    const handleLoginSuccess = (session: any) => {
        trackUserSource({
            component: 'EmbeddedTask',
            userId: session?.user?.id,
            isAuthenticated: true,
            loginSource: 'task_creation'
        });
    };

    const handleChipClick = (prompt: string) => {
        updateFormState({ taskInput: prompt });
        setExperimental(true);
    };

    const handleExperimentChange = (value: boolean) => {
        setExperimental(value);
        trackExperimentalFeature(value, {
            userId: user?.id,
            component: 'EmbeddedTask',
            source: 'manual_toggle'
        });
    };

    const onClearGithub = () => {
        updateFormState({ githubUrl: '', branchName: '' });
        if (repositorySelectorRef.current) {
            repositorySelectorRef.current.reset();
        }
    };

    // Add this helper function near the top of the component
        const isFieldVisible = (fieldId: string) => {
        const formLayout = globalConfig?.form_layouts?.[formState.projectType]?.cloud;
        const fieldConfig = formLayout?.fields?.find((field : any) => field.id === fieldId);
        return fieldConfig?.show !== false; // Default to true if not explicitly set to false
        };

    const debouncedSubmit = debounce(async () => {
        try {
            if (submissionInProgress.current) return;

            submissionInProgress.current = true;
            setIsSubmitting(true);

            console.log("EmbeddedTask - Starting task submission for tabId:", tabId);

            // Track task submission
            trackTaskCreation('submitted', {
                userId: user?.id,
                projectType: formState.projectType,
                isCloudFlow: true,
                taskInputLength: formState.taskInput.length,
                taskInput: formState.taskInput.substring(0, 100),
                hasImages: formState.selectedImages.length > 0,
                imageCount: formState.selectedImages.length,
                selectedModel: formState.selectedModel,
                selectedAgent: formState.selectedAgent,
                budget: formState.perInstanceCostLimit,
                tabId
            });

            if (!formState.selectedImage || !formState.taskInput) {
                toast({
                    title: "Error",
                    description: "Please fill in all required fields",
                    variant: "destructive",
                });
                return;
            }

            // Validate GitHub repository and branch selection
            if (formState.githubUrl && !formState.branchName) {
                toast({
                    title: "Branch Required",
                    description: "Please select a branch for the GitHub repository",
                    variant: "destructive",
                });
                return;
            }

            const tabStateData = {
                // Core form data with consistent field names
                task: formState.taskInput,
                selectedImage: formState.selectedImage,
                image: formState.selectedImage, // Duplicate for backward compatibility
                selectedAgent: formState.selectedAgent,
                model_name: formState.selectedModel,
                per_instance_cost_limit: formState.perInstanceCostLimit,
                agentic_skills: formState.selectedSkills,
                selectedImages: formState.selectedImages,
                repository: formState.githubUrl,
                branch: formState.branchName || '',
                projectType: formState.projectType,

                // Tab management fields
                fromCreateTask: true,
                tabId: tabId,
                parentTabId: tabId,
                isCloudFlow: true,
                client_ref_id: null,
                needsApiCall: true,
                pending_client_ref_id: crypto.randomUUID(),
                id: tabId,
                title: "Setting up Task",
                path: "/chat",
                experimental: experimental,

                // Image data
                base64_image_list: formState.selectedImages.length > 0 ?
                    formState.selectedImages.map(img => ({
                        mime_type: img.mime_type,
                        img_base64: img.img_base64
                    })) : undefined,

                // State metadata
                state: {
                    tabId: tabId,
                    sourceTabId: tabId,
                },
            };

            console.log("EmbeddedTask - Creating tab state data:", {
                task: tabStateData.task,
                selectedImage: tabStateData.selectedImage,
                image: tabStateData.image,
                selectedAgent: tabStateData.selectedAgent,
                model_name: tabStateData.model_name,
                tabId: tabId
            });

            imageAttachments.clearImages();

            console.log("EmbeddedTask - Updating tab state synchronously");

            // Update tab state synchronously
            updateTabState(tabId, tabStateData);

            // Update tabs synchronously
            setTabs((prevTabs) => {
                const tabExists = prevTabs.some(tab => tab.id === tabId);
                if (tabExists) {
                    return prevTabs.map((tab) =>
                        tab.id === tabId ? { ...tab, path: "/chat", title: "Setting up Task", state: tabStateData } : tab
                    );
                } else {
                    // Create new tab
                    return [...prevTabs, {
                        id: tabId,
                        title: "Setting up Task",
                        path: "/chat",
                        state: tabStateData
                    }];
                }
            });

            // Set active tab synchronously
            setActiveTab(tabId);
            onClearGithub();
            onClearTabId();

            console.log("EmbeddedTask - Task submission completed for tabId:", tabId);

        } catch (error) {
            console.error("Error in handleTaskSubmission:", error);
            toast({
                title: "Error",
                description: "Failed to create task. Please try again.",
                variant: "destructive",
            });

            trackTaskCreation('abandoned', {
                userId: user?.id,
                reason: 'submission_error',
                error: String(error),
                tabId
            });
        } finally {
            submissionInProgress.current = false;
            setIsSubmitting(false);
        }
    }, 700);

    const handleSubmit = () => {
        if (session && user) {
            debouncedSubmit();
        } else {
            setIsModalOpen(true);
        }
    };

    // Render functions
    const renderTaskDescription = () => (
        <div>
            <div className="relative">
                <TextareaWithAttachmentV2
                    value={formState.taskInput}
                    onChange={(e) => {
                        updateFormState({ taskInput: e.target.value });
                        // Track task input changes (debounced)
                        if (e.target.value.length % 50 === 0) {
                            trackTaskCreation('started', {
                                userId: user?.id,
                                taskInputLength: e.target.value.length,
                                isTyping: true,
                                tabId
                            });
                        }
                    }}
                    handleExperimentalChange={handleExperimentChange}
                    experimentalEnabled={experimental}
                    className="relative bg-[#141415] border border-[#2C2C2D] rounded-[16px] text-[#DDDDE6]
                    placeholder:text-[#666] md:text-base focus-visible:ring-1 focus:ring-1 z-10"
                    onAttachClick={imageAttachments.openFilePicker}
                    attachIcon={AttachIcon}
                    attachTooltip="Attach images (max 5 images, 5MB each)"
                    onPaste={(e) => {
                        const items = e.clipboardData?.items;
                        if (!items) return;

                        const imageItems = Array.from(items).filter(item => item.type.startsWith('image/'));
                        if (imageItems.length === 0) return;

                        e.preventDefault();
                        const files = imageItems.map(item => item.getAsFile()).filter(Boolean) as File[];
                        if (files.length > 0) {
                            imageAttachments.handleImageSelect(files);
                        }
                    }}
                    handleSettingsClick={() => {
                        if (isTransitioning) return;
                        if (showGithubSettings) {
                            setIsTransitioning(true);
                            setShowGithubSettings(false);
                            setTimeout(() => {
                                setShowControls(!showControls);
                                setIsTransitioning(false);
                            }, 300);
                        } else {
                            setShowControls(!showControls);
                        }
                    }}
                    showControls={showControls}
                    onSubmit={handleSubmit}
                    disabled={isSubmitting || !formState.taskInput || isInputDisabled()}
                    isSubmitting={isSubmitting}
                    showGithubSettings={showGithubSettings}
                    handleGithubSettings={() => {
                        if (!session) {
                            setIsModalOpen(true);
                            return;
                        }
                        if (isTransitioning) return;
                        if (showControls) {
                            setIsTransitioning(true);
                            setShowControls(false);
                            setTimeout(() => {
                                setShowGithubSettings(!showGithubSettings);
                                setIsTransitioning(false);
                            }, 300);
                        } else {
                            setShowGithubSettings(!showGithubSettings);
                        }
                    }}
                    hasUserConnectedGithub={hasUserConnectedGithub}
                    githubUrl={formState.githubUrl}
                    onClearGithubUrl={onClearGithub}
                />
            </div>

            {/* Selected Images Preview */}
            {formState.selectedImages.length > 0 && (
                <div className="mt-[-20px] max-w-[94%] mx-auto bg-[#1D1D1F] border border-[#222] rounded-lg p-3">
                    <div className="flex flex-wrap gap-3 pt-4">
                        {formState.loadingImages && (
                            <div className="flex items-center justify-center w-full py-4">
                                <Loader2 className="h-6 w-6 text-[#999] animate-spin mr-2" />
                                <span className="text-[#999]">Processing images...</span>
                            </div>
                        )}
                        {!formState.loadingImages && formState.selectedImages.map((image, index) => (
                            <div
                                key={index}
                                className="relative w-12 h-12 rounded-md border border-[#333] group"
                            >
                                <img
                                    src={`data:${image.mime_type};base64,${image.img_base64}`}
                                    alt={`Selected image ${index + 1}`}
                                    className="w-full h-full object-cover z-[1] rounded-md"
                                />
                                <button
                                    type="button"
                                    onClick={() => imageAttachments.removeImage(index)}
                                    className="absolute top-[-8px] right-[-8px] bg-transparent z-[2] rounded-full p-1 transition-opacity"
                                    aria-label="Remove image"
                                >
                                    <div className="p-1 bg-white rounded-full">
                                        <img src={AttachmentCross} alt="Remove image" className="w-3 h-3" />
                                    </div>
                                </button>
                            </div>
                        ))}
                    </div>
                </div>
            )}
        </div>
    );

    const renderAdvancedControls = () => (
      <div className="w-full bg-[#0F181A] border border-[#66EAFF1F] rounded-lg transition-colors mt-3">
        <div className="flex items-center justify-between w-full gap-4 px-5 pt-5">
          <div className="flex items-center gap-2">
            <img
              alt="Advanced Controls"
              src={AdvancedControls}
              className="w-6 h-6"
            />
            <span className="text-[#66EAFF] font-brockmann font-medium">
              Advanced Controls
            </span>
          </div>
          <img
            alt="Close"
            src={CloseSVG}
            className="w-5 h-5 cursor-pointer"
            onClick={() => {
              if (!isTransitioning) {
                setShowControls(false);
              }
            }}
          />
        </div>

        <div className="grid grid-cols-1 gap-5 px-5 py-5 md:grid-cols-2">
          {/* Docker Image Selector */}
          {isFieldVisible("dockerImage") && (
            <DockerImageSelector
              selectedImage={formState.selectedImage}
              setSelectedImage={(image) =>
                updateFormState({ selectedImage: image })
              }
              globalConfig={globalConfig as any}
              label="Select Template"
              isCloudFlow={true}
            />
          )}

          {/* Budget Control */}
          {isFieldVisible('budget') && <div className="flex flex-col justify-between h-full">
            <p className="text-[#FFFFFF50] font-inter text-sm">Budget (ECU)</p>
            <div className="relative flex items-center">
              <button
                type="button"
                title="Decrease budget"
                onClick={() => {
                  const current = parseInt(formState.perInstanceCostLimit);
                  if (current > 1) {
                    updateFormState({
                      perInstanceCostLimit: (current - 1).toString(),
                    });
                  }
                }}
                className="w-[40px] h-[40px] flex items-center justify-center absolute left-1 text-[#666] hover:text-[#999] transition-colors hover:bg-[#0F181A] rounded-sm"
              >
                <Minus className="h-6 w-6 text-[#DDDDE6]" />
              </button>
              <div className="w-full h-[56px] bg-[#FFFFFF0A] border border-[#ffffff12] rounded-lg text-[#DDDDE6] px-4 flex items-center justify-center hover:border-[#333]">
                <img
                  alt="Budget"
                  className="text-[#B4A456] w-5 h-5"
                  src={CoinIconSVG}
                />
                <input
                  type="number"
                  min="1"
                  step="1"
                  value={formState.perInstanceCostLimit}
                  onChange={(e) => {
                    if (e.target.value !== "0") {
                      updateFormState({ perInstanceCostLimit: e.target.value });
                    }
                  }}
                  disabled={configLoading}
                  placeholder={configLoading ? "Loading..." : " - - "}
                  className="w-20 bg-transparent text-left focus:outline-none text-[#DDDDE6] font-inter text-xl font-semibold leading-[120%] tracking-[-0.4px] [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                />
              </div>
              <button
                type="button"
                title="Increase budget"
                onClick={() => {
                  const current = parseInt(formState.perInstanceCostLimit);
                  updateFormState({
                    perInstanceCostLimit: (current + 1).toString(),
                  });
                }}
                className="w-[40px] h-[40px] flex items-center justify-center absolute right-1 text-[#666] hover:text-[#999] transition-colors hover:bg-[#0F181A] rounded-sm"
              >
                <Plus className="h-6 w-6 text-[#DDDDE6]" />
              </button>
            </div>
          </div> }
        </div>

        <div className="grid grid-cols-1 gap-5 px-5 py-5">
          {/* Agent Selector */}
          {isFieldVisible('agent') && <div className="flex flex-col space-y-2">
            <p className="text-[#FFFFFF50] font-inter text-sm">Agent</p>
            <Select
              value={formState.selectedAgent}
              onValueChange={(value: string) =>
                updateFormState({ selectedAgent: value })
              }
              disabled={configLoading}
            >
              <SelectTrigger className="w-full h-14 bg-[#FFFFFF0A] border border-[#ffffff12] rounded-lg text-[#DDDDE6] hover:border-[#333] transition-colors data-[state=open]:border-[#333]">
                {configLoading ? (
                  <div className="flex items-center gap-2">
                    <Loader2 className="w-4 h-4 animate-spin" />
                    <span>Loading agents...</span>
                  </div>
                ) : (
                  <SelectValue placeholder="Select Agent" />
                )}
              </SelectTrigger>
              <SelectContent className="bg-[#131314] border border-[#222]">
                {globalConfig?.agent_names?.map((agent: any) => (
                  <SelectItem
                    key={agent.name}
                    value={agent.name}
                    className="text-[#DDDDE6] hover:bg-[#222] focus:bg-[#222]"
                  >
                    {agent.display_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>}

          {/* Base Model Selector */}
          {isFieldVisible('baseModel') && <div className="flex flex-col space-y-2">
            <p className="text-[#FFFFFF50] font-inter text-sm">Base Model</p>
            <Select
              value={formState.selectedModel}
              onValueChange={(value: string) =>
                updateFormState({ selectedModel: value })
              }
              disabled={configLoading}
            >
              <SelectTrigger className="w-full h-12 bg-[#FFFFFF0A] border border-[#ffffff12] rounded-lg text-[#DDDDE6] hover:border-[#ffffff30] transition-colors data-[state=open]:border-[#ffffff30]">
                <div className="flex items-center gap-2">
                  <img alt="" src={CuteAIIcon} className="w-5 h-5" />
                  {configLoading ? (
                    <div className="flex items-center gap-2">
                      <Loader2 className="w-4 h-4 animate-spin" />
                      <span>Loading models...</span>
                    </div>
                  ) : (
                    <SelectValue placeholder="Select Model" />
                  )}
                </div>
              </SelectTrigger>
              <SelectContent className="bg-[#131314] border border-[#222]">
                {globalConfig?.model_list?.map((model: any) => (
                  <SelectItem
                    key={model.name}
                    value={model.name}
                    className="text-[#DDDDE6] hover:bg-[#222] cursor-pointer"
                  >
                    {model.display_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div> }

          {/* Skills Selector */}
          {isFieldVisible('skills') && <SkillsSelector
            selectedSkills={formState.selectedSkills}
            setSelectedSkills={(skills) =>
              updateFormState({ selectedSkills: skills })
            }
            skills={skills}
            loadingSkills={false}
            label="Skills"
          /> }
        </div>
      </div>
    );

    if (!globalConfig) {
        return (
            <div className="flex items-center min-h-[300px] justify-center w-full h-full">
                <Loader2 className="w-8 h-8 text-white opacity-25 animate-spin" />
            </div>
        );
    }

    return (
        <>
            <AuthModal
                enableWelcomeModal={true}
                open={isModalOpen}
                onOpenChange={setIsModalOpen}
                defaultView="signup"
                onSuccess={handleLoginSuccess}
            />
            <Tooltip.Provider delayDuration={200}>
                <div className="relative">
                    <div className="w-full max-w-4xl p-6 mx-auto md:pt-10">
                        {/* Main Task Description */}
                        <motion.div
                            initial={{ opacity: 0, y: -20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{
                                duration: 0.3,
                                ease: "easeOut"
                            }}
                        >
                            {renderTaskDescription()}
                        </motion.div>

                        {/* Suggestion Chips */}
                        {formState.selectedImages.length === 0 && (
                            <SuggestionChips
                                config={globalConfig as any}
                                showChips={showChips && !showControls && (isMobile || !showGithubSettings)}
                                taskInput={formState.taskInput}
                                onChipClick={handleChipClick}
                                onSurpriseMe={() => {}}
                            />
                        )}

                        {/* GitHub Settings */}
                        <AnimatePresence>
                            {!showControls && showGithubSettings && (
                                <motion.div
                                    initial={{ opacity: 0, height: 0, scale: 0.8 }}
                                    animate={{ opacity: 1, height: 'auto', scale: 1 }}
                                    exit={{ opacity: 0, height: 0, scale: 0.8 }}
                                    transition={{ duration: 0.3, ease: "easeInOut" }}
                                >
                                    <div className="w-full bg-[#131314] mt-3">
                                        <RepositorySelectorV2
                                            ref={repositorySelectorRef}
                                            githubUrl={formState.githubUrl}
                                            branchName={formState.branchName}
                                            onGithubUrlChange={(url: string) => updateFormState({ githubUrl: url })}
                                            onBranchNameChange={(branch: string) => updateFormState({ branchName: branch })}
                                            onClose={() => {
                                                setShowGithubSettings(false);
                                            }}
                                        />
                                    </div>
                                </motion.div>
                            )}

                            {/* Advanced Controls */}
                            {showControls && !showGithubSettings && (
                                <motion.div
                                    initial={{ opacity: 0, height: 0, scale: 0.8 }}
                                    animate={{ opacity: 1, height: 'auto', scale: 1 }}
                                    exit={{ opacity: 0, height: 0, scale: 0.8 }}
                                    transition={{ duration: 0.3, ease: "easeInOut" }}
                                >
                                    {renderAdvancedControls()}
                                </motion.div>
                            )}
                        </AnimatePresence>
                    </div>
                </div>
            </Tooltip.Provider>
        </>
    );
}